import React, { useRef, useEffect } from 'react';
import { Card } from "@/components/ui/card";
import { TrendingUp, Target, Zap, Droplets } from "lucide-react";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import HighchartsContextMenu from './HighchartsContextMenu';

// Import exporting modules
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsExportData from 'highcharts/modules/export-data';

// Initialize modules
HighchartsExporting(Highcharts);
HighchartsExportData(Highcharts);

const ChartGridWithContextMenu: React.FC = () => {
  // Chart refs for context menu functionality
  const chart1Ref = useRef<HighchartsReact.RefObject>(null);
  const chart2Ref = useRef<HighchartsReact.RefObject>(null);
  const chart3Ref = useRef<HighchartsReact.RefObject>(null);
  const chart4Ref = useRef<HighchartsReact.RefObject>(null);

  // Sample data for charts
  const sampleData = [
    { name: 'Jan', value: 65 },
    { name: 'Feb', value: 78 },
    { name: 'Mar', value: 82 },
    { name: 'Apr', value: 75 },
    { name: 'May', value: 88 },
    { name: 'Jun', value: 92 }
  ];

  // Chart configurations
  const getChartOptions = (title: string, type: string = 'line', color: string = '#3b82f6') => ({
    chart: {
      type: type,
      height: 250,
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null // We'll use the card title instead
    },
    xAxis: {
      categories: sampleData.map(d => d.name),
      labels: {
        style: {
          fontSize: '11px'
        }
      }
    },
    yAxis: {
      title: {
        text: 'Value'
      },
      labels: {
        style: {
          fontSize: '11px'
        }
      }
    },
    series: [{
      name: title,
      data: sampleData.map(d => d.value),
      color: color,
      lineWidth: 2,
      marker: {
        radius: 4
      }
    }],
    legend: {
      enabled: false
    },
    credits: {
      enabled: false
    },
    exporting: {
      enabled: false // We use our custom context menu instead
    },
    tooltip: {
      useHTML: true,
      headerFormat: '<b>{point.x}</b><br/>',
      pointFormat: '{series.name}: <b>{point.y}</b>'
    }
  });

  const chartCards = [
    {
      title: "Monthly Trends",
      subtitle: "Performance over time",
      icon: TrendingUp,
      color: "bg-blue-500",
      chartOptions: getChartOptions("Monthly Trends", "line", "#3b82f6"),
      ref: chart1Ref
    },
    {
      title: "Target Achievement",
      subtitle: "Goal completion rates",
      icon: Target,
      color: "bg-green-500",
      chartOptions: getChartOptions("Target Achievement", "column", "#10b981"),
      ref: chart2Ref
    },
    {
      title: "Performance Metrics",
      subtitle: "Key indicators",
      icon: Zap,
      color: "bg-purple-500",
      chartOptions: getChartOptions("Performance Metrics", "area", "#8b5cf6"),
      ref: chart3Ref
    },
    {
      title: "Efficiency Trends",
      subtitle: "Resource optimization",
      icon: Droplets,
      color: "bg-cyan-500",
      chartOptions: getChartOptions("Efficiency Trends", "spline", "#06b6d4"),
      ref: chart4Ref
    }
  ];

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            Charts with Context Menu
          </h3>
          <p className="text-gray-600">
            4-box chart layout with Highcharts context menu functionality
          </p>
        </div>
      </div>

      {/* Chart Grid - 2x2 Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card 
              key={index}
              className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Card Header with Context Menu */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${card.color} text-white`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{card.title}</h4>
                    <p className="text-sm text-gray-500">{card.subtitle}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-green-600">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm font-medium">+12%</span>
                  </div>
                  <HighchartsContextMenu 
                    chartTitle={card.title} 
                    chartRef={card.ref}
                  />
                </div>
              </div>

              {/* Chart Container */}
              <div className="bg-gradient-to-br from-gray-50 to-white rounded-lg p-3 border border-gray-100">
                <HighchartsReact
                  ref={card.ref}
                  highcharts={Highcharts}
                  options={card.chartOptions}
                  containerProps={{ style: { height: '100%', width: '100%' } }}
                />
              </div>

              {/* Quick Stats */}
              <div className="mt-4 flex justify-between text-sm">
                <span className="text-gray-500">Last updated: 2 min ago</span>
                <button className="text-blue-600 hover:text-blue-800 font-medium">
                  View Details →
                </button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Usage Instructions */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-3">Context Menu Features</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h5 className="font-medium text-gray-800 mb-2">Export Options:</h5>
            <ul className="space-y-1">
              <li>• Export as PNG image</li>
              <li>• Export as JPEG image</li>
              <li>• Export as PDF document</li>
              <li>• Export as SVG vector</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-800 mb-2">Additional Actions:</h5>
            <ul className="space-y-1">
              <li>• Print chart directly</li>
              <li>• View in fullscreen mode</li>
              <li>• Share chart link</li>
              <li>• Right-aligned menu button</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChartGridWithContextMenu;

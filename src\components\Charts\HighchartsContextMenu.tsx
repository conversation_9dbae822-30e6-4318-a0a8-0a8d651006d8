import React, { useRef } from 'react';
import { 
  MoreVertical, 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Printer, 
  Share2, 
  Maximize2,
  Image,
  FileImage
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HighchartsContextMenuProps {
  chartTitle: string;
  chartRef?: React.RefObject<any>;
  onFullscreen?: () => void;
  onShare?: () => void;
  className?: string;
}

const HighchartsContextMenu: React.FC<HighchartsContextMenuProps> = ({
  chartTitle,
  chartRef,
  onFullscreen,
  onShare,
  className = ""
}) => {
  const handleExportPNG = () => {
    console.log(`Exporting ${chartTitle} as PNG`);
    if (chartRef?.current?.chart) {
      try {
        chartRef.current.chart.exportChart({
          type: 'image/png',
          filename: `${chartTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart`
        });
      } catch (error) {
        console.error('Error exporting PNG:', error);
      }
    }
  };

  const handleExportJPEG = () => {
    console.log(`Exporting ${chartTitle} as JPEG`);
    if (chartRef?.current?.chart) {
      try {
        chartRef.current.chart.exportChart({
          type: 'image/jpeg',
          filename: `${chartTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart`
        });
      } catch (error) {
        console.error('Error exporting JPEG:', error);
      }
    }
  };

  const handleExportPDF = () => {
    console.log(`Exporting ${chartTitle} as PDF`);
    if (chartRef?.current?.chart) {
      try {
        chartRef.current.chart.exportChart({
          type: 'application/pdf',
          filename: `${chartTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart`
        });
      } catch (error) {
        console.error('Error exporting PDF:', error);
      }
    }
  };

  const handleExportSVG = () => {
    console.log(`Exporting ${chartTitle} as SVG`);
    if (chartRef?.current?.chart) {
      try {
        chartRef.current.chart.exportChart({
          type: 'image/svg+xml',
          filename: `${chartTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_chart`
        });
      } catch (error) {
        console.error('Error exporting SVG:', error);
      }
    }
  };

  const handlePrint = () => {
    console.log(`Printing ${chartTitle}`);
    if (chartRef?.current?.chart) {
      try {
        chartRef.current.chart.print();
      } catch (error) {
        console.error('Error printing chart:', error);
        // Fallback to window print
        window.print();
      }
    } else {
      window.print();
    }
  };

  const handleFullscreen = () => {
    console.log(`Opening ${chartTitle} in fullscreen`);
    if (onFullscreen) {
      onFullscreen();
    } else {
      // Default fullscreen implementation
      console.log('Fullscreen modal would open here');
    }
  };

  const handleShare = () => {
    console.log(`Sharing ${chartTitle}`);
    if (onShare) {
      onShare();
    } else {
      // Default share implementation
      if (navigator.share) {
        navigator.share({
          title: chartTitle,
          text: `Check out this chart: ${chartTitle}`,
          url: window.location.href
        }).catch(console.error);
      } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href)
          .then(() => {
            console.log('Link copied to clipboard');
            // You could show a toast notification here
          })
          .catch(console.error);
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button 
          className={`p-1 rounded-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${className}`}
          aria-label={`Chart options for ${chartTitle}`}
        >
          <MoreVertical className="h-4 w-4 text-gray-500" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Chart Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={handleExportPNG} className="cursor-pointer">
          <Image className="mr-2 h-4 w-4" />
          Export as PNG
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleExportJPEG} className="cursor-pointer">
          <FileImage className="mr-2 h-4 w-4" />
          Export as JPEG
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleExportPDF} className="cursor-pointer">
          <FileText className="mr-2 h-4 w-4" />
          Export as PDF
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleExportSVG} className="cursor-pointer">
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          Export as SVG
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={handlePrint} className="cursor-pointer">
          <Printer className="mr-2 h-4 w-4" />
          Print Chart
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleFullscreen} className="cursor-pointer">
          <Maximize2 className="mr-2 h-4 w-4" />
          View Fullscreen
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleShare} className="cursor-pointer">
          <Share2 className="mr-2 h-4 w-4" />
          Share Chart
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default HighchartsContextMenu;

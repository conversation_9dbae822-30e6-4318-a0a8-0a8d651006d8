# Chart Context Menu Components

This directory contains reusable chart context menu components that provide Highcharts-style context menu functionality for charts in a 4-box grid layout.

## Components

### 1. ChartContextMenu
A generic context menu component that can be used with any chart library (Recharts, Chart.js, etc.).

**Props:**
- `chartTitle: string` - The title of the chart
- `chartData?: any` - Chart data (optional)
- `chartRef?: React.RefObject<any>` - Reference to the chart component
- `onExport?: (format: string) => void` - Custom export handler
- `onPrint?: () => void` - Custom print handler
- `onFullscreen?: () => void` - Custom fullscreen handler
- `onShare?: () => void` - Custom share handler
- `className?: string` - Additional CSS classes

### 2. HighchartsContextMenu
A specialized context menu component designed specifically for Highcharts with built-in export functionality.

**Props:**
- `chartTitle: string` - The title of the chart
- `chartRef?: React.RefObject<any>` - Reference to the HighchartsReact component
- `onFullscreen?: () => void` - Custom fullscreen handler
- `onShare?: () => void` - Custom share handler
- `className?: string` - Additional CSS classes

### 3. ChartGridWithContextMenu
A complete demo component showing a 4-box chart layout with context menus.

## Usage Examples

### Basic Usage with Highcharts

```tsx
import React, { useRef } from 'react';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import HighchartsContextMenu from './HighchartsContextMenu';

// Enable exporting modules
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsExportData from 'highcharts/modules/export-data';
HighchartsExporting(Highcharts);
HighchartsExportData(Highcharts);

const MyChart = () => {
  const chartRef = useRef<HighchartsReact.RefObject>(null);

  const chartOptions = {
    // Your Highcharts configuration
    exporting: {
      enabled: false // Disable default menu, use our custom one
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>My Chart Title</CardTitle>
            <CardDescription>Chart description</CardDescription>
          </div>
          <HighchartsContextMenu 
            chartTitle="My Chart Title" 
            chartRef={chartRef}
          />
        </div>
      </CardHeader>
      <CardContent>
        <HighchartsReact
          ref={chartRef}
          highcharts={Highcharts}
          options={chartOptions}
        />
      </CardContent>
    </Card>
  );
};
```

### 4-Box Grid Layout

```tsx
import ChartGridWithContextMenu from './ChartGridWithContextMenu';

const Dashboard = () => {
  return (
    <div className="space-y-6">
      <ChartGridWithContextMenu />
    </div>
  );
};
```

### Custom Export Handlers

```tsx
const MyChart = () => {
  const handleCustomExport = (format: string) => {
    switch (format) {
      case 'png':
        // Custom PNG export logic
        break;
      case 'pdf':
        // Custom PDF export logic
        break;
      // ... other formats
    }
  };

  return (
    <ChartContextMenu
      chartTitle="My Chart"
      onExport={handleCustomExport}
    />
  );
};
```

## Features

### Export Options
- **PNG Export**: High-quality raster image export
- **JPEG Export**: Compressed image export
- **PDF Export**: Vector-based document export
- **SVG Export**: Scalable vector graphics export

### Additional Actions
- **Print**: Direct chart printing functionality
- **Fullscreen**: View chart in fullscreen mode
- **Share**: Share chart via native sharing API or clipboard

## Styling

The context menu is positioned on the same row as the chart title, aligned to the right side. It uses a subtle three-dot (MoreVertical) icon that appears on hover.

### CSS Classes
- Hover effects with smooth transitions
- Focus states for accessibility
- Responsive design for mobile devices
- Consistent with the platform's design system

## Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- Focus management
- High contrast support

## Browser Support

- Modern browsers with ES6+ support
- Native sharing API where available
- Clipboard API fallback for sharing
- Print API support

## Dependencies

- React 18+
- Lucide React (for icons)
- Radix UI (for dropdown menu primitives)
- Highcharts (for HighchartsContextMenu)
- Highcharts React (for HighchartsContextMenu)

## Notes

1. **Highcharts Modules**: Make sure to import and initialize the exporting modules:
   ```tsx
   import HighchartsExporting from 'highcharts/modules/exporting';
   import HighchartsExportData from 'highcharts/modules/export-data';
   HighchartsExporting(Highcharts);
   HighchartsExportData(Highcharts);
   ```

2. **Chart References**: Always use refs to access chart instances for export functionality.

3. **Disable Default Menus**: Set `exporting.enabled: false` in Highcharts options to use custom context menu.

4. **Grid Layout**: Use `grid grid-cols-1 lg:grid-cols-2 gap-6` for responsive 4-box layout.

## Demo

Visit the "Chart Context Menu" tab in the Dashboard to see a live demo of all functionality.

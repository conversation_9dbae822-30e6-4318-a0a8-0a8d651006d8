import React from 'react';
import { Card } from "@/components/ui/card";
import { TrendingUp, Target, Zap, Droplets, MoreVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Simple Chart Context Menu Component
const SimpleChartContextMenu = ({ chartTitle }: { chartTitle: string }) => {
  const handleAction = (action: string) => {
    console.log(`${action} clicked for ${chartTitle}`);
    alert(`${action} clicked for ${chartTitle}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button 
          className="p-1 rounded-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          aria-label={`Chart options for ${chartTitle}`}
        >
          <MoreVertical className="h-4 w-4 text-gray-500" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Chart Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => handleAction('Export as PNG')} className="cursor-pointer">
          📊 Export as PNG
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => handleAction('Export as PDF')} className="cursor-pointer">
          📄 Export as PDF
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => handleAction('Print Chart')} className="cursor-pointer">
          🖨️ Print Chart
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => handleAction('View Fullscreen')} className="cursor-pointer">
          🔍 View Fullscreen
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => handleAction('Share Chart')} className="cursor-pointer">
          📤 Share Chart
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Simple Chart Placeholder Component
const SimpleChart = ({ title, color }: { title: string; color: string }) => {
  return (
    <div className="h-64 bg-gradient-to-br from-gray-50 to-white rounded-lg p-4 border border-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className={`w-16 h-16 ${color} rounded-full mx-auto mb-4 flex items-center justify-center`}>
          <TrendingUp className="w-8 h-8 text-white" />
        </div>
        <h4 className="text-lg font-semibold text-gray-700">{title}</h4>
        <p className="text-sm text-gray-500 mt-2">Sample chart data visualization</p>
        <div className="mt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Current:</span>
            <span className="font-medium">85%</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Target:</span>
            <span className="font-medium">90%</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Trend:</span>
            <span className="font-medium text-green-600">↗ +5%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const SimpleChartGrid: React.FC = () => {
  const chartCards = [
    {
      title: "Monthly Trends",
      subtitle: "Performance over time",
      icon: TrendingUp,
      color: "bg-blue-500"
    },
    {
      title: "Target Achievement",
      subtitle: "Goal completion rates",
      icon: Target,
      color: "bg-green-500"
    },
    {
      title: "Performance Metrics",
      subtitle: "Key indicators",
      icon: Zap,
      color: "bg-purple-500"
    },
    {
      title: "Efficiency Trends",
      subtitle: "Resource optimization",
      icon: Droplets,
      color: "bg-cyan-500"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            Chart Context Menu Demo
          </h3>
          <p className="text-gray-600">
            4-box chart layout with context menu positioned on the same row as titles, aligned to the right
          </p>
        </div>
      </div>

      {/* Chart Grid - 2x2 Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {chartCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card 
              key={index}
              className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Card Header with Context Menu - Same row, right aligned */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${card.color} text-white`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{card.title}</h4>
                    <p className="text-sm text-gray-500">{card.subtitle}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-green-600">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm font-medium">+12%</span>
                  </div>
                  {/* Context Menu positioned on the right */}
                  <SimpleChartContextMenu chartTitle={card.title} />
                </div>
              </div>

              {/* Chart Container */}
              <SimpleChart title={card.title} color={card.color} />

              {/* Quick Stats */}
              <div className="mt-4 flex justify-between text-sm">
                <span className="text-gray-500">Last updated: 2 min ago</span>
                <button className="text-blue-600 hover:text-blue-800 font-medium">
                  View Details →
                </button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Usage Instructions */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-3">Context Menu Features</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h5 className="font-medium text-gray-800 mb-2">Positioning:</h5>
            <ul className="space-y-1">
              <li>✅ Same row as chart title</li>
              <li>✅ Right-aligned positioning</li>
              <li>✅ Three-dot menu icon</li>
              <li>✅ Hover effects</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-800 mb-2">Menu Options:</h5>
            <ul className="space-y-1">
              <li>• Export as PNG/PDF</li>
              <li>• Print chart</li>
              <li>• Fullscreen view</li>
              <li>• Share functionality</li>
            </ul>
          </div>
        </div>
        <div className="mt-4 p-3 bg-white rounded-lg border border-blue-200">
          <p className="text-sm text-gray-700">
            <strong>Click the three-dot menu (⋮) on any chart</strong> to see the context menu in action. 
            The menu appears aligned to the right side of each chart title.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default SimpleChartGrid;

import React, { useMemo, useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { format, subMonths, startOfMonth, endOfMonth, addMonths } from 'date-fns';
import { Observation } from '@/types/observation';
import {
  PieChart as PieChartIcon,
  TrendingUp,
  Users
} from 'lucide-react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import HighchartsContextMenu from '../Charts/HighchartsContextMenu';

interface ObservationAnalyticsChartsProps {
  observations?: Observation[];
}

const ObservationAnalyticsCharts: React.FC<ObservationAnalyticsChartsProps> = ({
  observations = []
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalData, setModalData] = useState([]);

  // Chart refs for context menu functionality
  const categoryChartRef = useRef<HighchartsReact.RefObject>(null);
  const rollingChartRef = useRef<HighchartsReact.RefObject>(null);
  const reportersChartRef = useRef<HighchartsReact.RefObject>(null);

  // Columns for the data table (from All Observations tab)
  const observationColumns = [
    { key: 'maskId', header: 'ID', sortable: true, width: 'w-[120px]', render: (value) => (<span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">{value}</span>) },
    { key: 'location', header: 'Location', width: 'w-[180px]', filterable: true, filterType: 'text' },
    { key: 'category', header: 'Category', width: 'w-[140px]', filterable: true, filterType: 'select' },
    { key: 'type', header: 'Type', width: 'w-[120px]', filterable: true, filterType: 'select' },
    { key: 'reportedBy', header: 'Reported By', width: 'w-[140px]', filterable: true, filterType: 'text' },
    { key: 'reportedDate', header: 'Reported Date', width: 'w-[120px]', sortable: true, render: (value) => { if (!value) return '-'; try { const date = new Date(value); if (isNaN(date.getTime())) return '-'; return format(date, 'MM/dd/yyyy'); } catch { return '-'; } } },
    { key: 'actionAssignee', header: 'Action Assignee', width: 'w-[140px]', filterable: true, filterType: 'text' },
    { key: 'reviewedBy', header: 'Reviewed By', width: 'w-[140px]', filterable: true, filterType: 'text', render: (value) => value || '-' },
    { key: 'status', header: 'Status', width: 'w-[120px]', filterable: true, filterType: 'select' },
  ];

  // Helper function to get observations by time period
  const getObservationsByTimePeriod = (observations: Observation[], months: number) => {
    const cutoffDate = subMonths(new Date(), months);
    return observations.filter(obs => {
      const obsDate = new Date(obs.reportedDate);
      return obsDate >= cutoffDate;
    });
  };

  // Helper function to group observations by category
  const groupObservationsByCategory = (observations: Observation[]) => {
    const grouped = observations.reduce((acc, obs) => {
      if (!acc[obs.category]) {
        acc[obs.category] = 0;
      }
      acc[obs.category]++;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(grouped).map(([name, value]) => ({ name, value }));
  };

  // Helper function to group observations by type (nature)
  const groupObservationsByType = (observations: Observation[]) => {
    const grouped = observations.reduce((acc, obs) => {
      if (!acc[obs.type]) {
        acc[obs.type] = 0;
      }
      acc[obs.type]++;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(grouped).map(([name, value]) => ({ name, value }));
  };

  // Helper function to group observations by reporter
  const groupObservationsByReporter = (observations: Observation[]) => {
    const grouped = observations.reduce((acc, obs) => {
      if (!acc[obs.reportedBy]) {
        acc[obs.reportedBy] = 0;
      }
      acc[obs.reportedBy]++;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(grouped)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 reporters
  };

  // Helper function to generate 12-month rolling data with month names
  const generate12MonthRollingData = (observations: Observation[], groupBy: 'category' | 'type') => {
    const months: string[] = [];
    const seriesData: Record<string, number[]> = {};
    const now = new Date();
    // Generate last 12 months with month abbreviations
    for (let i = 11; i >= 0; i--) {
      const monthDate = addMonths(new Date(now.getFullYear(), now.getMonth(), 1), -i);
      const monthStart = startOfMonth(monthDate);
      const monthEnd = endOfMonth(monthDate);
      const monthKey = format(monthStart, 'MMM'); // Only month abbreviation
      months.push(monthKey);
      // Get observations for this month
      const monthObservations = observations.filter(obs => {
        const obsDate = new Date(obs.reportedDate);
        return obsDate >= monthStart && obsDate <= monthEnd;
      });
      // Group by category or type
      const grouped = monthObservations.reduce((acc, obs) => {
        const key = groupBy === 'category' ? obs.category : obs.type;
        if (!acc[key]) {
          acc[key] = 0;
        }
        acc[key]++;
        return acc;
      }, {} as Record<string, number>);
      // Add to series data
      Object.entries(grouped).forEach(([key, value]) => {
        if (!seriesData[key]) {
          seriesData[key] = new Array(12).fill(0);
        }
        seriesData[key][11 - i] = value;
      });
    }
    return { months, seriesData };
  };

  // Get data for charts
  const allObservations = observations;
  const last12MonthsObservations = getObservationsByTimePeriod(observations, 12);
  
  const categoryData = useMemo(() => groupObservationsByCategory(allObservations), [allObservations]);
  const typeData = useMemo(() => groupObservationsByType(allObservations), [allObservations]);
  const reporterData = useMemo(() => groupObservationsByReporter(allObservations), [allObservations]);
  
  const categoryRollingData = useMemo(() => generate12MonthRollingData(last12MonthsObservations, 'category'), [last12MonthsObservations]);
  const typeRollingData = useMemo(() => generate12MonthRollingData(last12MonthsObservations, 'type'), [last12MonthsObservations]);

  // Chart colors
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'];

  // Handler for chart clicks
  const handleChartClick = (filterKey, filterValue, title) => {
    const filtered = allObservations.filter(obs => obs[filterKey] === filterValue);
    setModalTitle(title);
    setModalData(filtered);
    setModalOpen(true);
  };

  // 1. Breakdown of observations by Category
  const categoryChartOptions: Highcharts.Options = {
    chart: {
      type: 'pie',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y}</b> ({point.percentage:.1f}%)'
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.y} ({point.percentage:.1f}%)',
          style: {
            fontSize: '11px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: categoryData.map((item, index) => ({
        name: item.name,
        y: item.value,
        color: COLORS[index % COLORS.length]
      }))
    }],
    credits: {
      enabled: false
    }
  };

  // Add plotOptions for click events
  const categoryChartOptionsWithClick = {
    ...categoryChartOptions,
    plotOptions: {
      ...categoryChartOptions.plotOptions,
      pie: {
        ...categoryChartOptions.plotOptions?.pie,
        point: {
          events: {
            click: function () {
              handleChartClick('category', this.name, `Observations for Category: ${this.name}`);
            }
          }
        }
      }
    }
  };

  // 2. Breakdown of observations by Nature (Type)
  const natureChartOptions: Highcharts.Options = {
    chart: {
      type: 'pie',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y}</b> ({point.percentage:.1f}%)'
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.y} ({point.percentage:.1f}%)',
          style: {
            fontSize: '11px'
          }
        },
        showInLegend: true
      }
    },
    series: [{
      name: 'Observations',
      colorByPoint: true,
      data: typeData.map((item, index) => ({
        name: item.name,
        y: item.value,
        color: item.name === 'Safe' ? '#10b981' : '#ef4444'
      }))
    }],
    credits: {
      enabled: false
    }
  };

  // Add plotOptions for click events
  const natureChartOptionsWithClick = {
    ...natureChartOptions,
    plotOptions: {
      ...natureChartOptions.plotOptions,
      pie: {
        ...natureChartOptions.plotOptions?.pie,
        point: {
          events: {
            click: function () {
              handleChartClick('type', this.name, `Observations for Type: ${this.name}`);
            }
          }
        }
      }
    }
  };

  // 3. 12-month rolling breakdown by Nature of observations
  const natureRollingChartOptions: Highcharts.Options = {
    chart: {
      type: 'column',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    xAxis: {
      categories: typeRollingData.months,
      crosshair: true,
      labels: {
        rotation: 0,
        style: {
          fontSize: '13px'
        }
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Observations'
      }
    },
    tooltip: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>{point.y}</b></td></tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0
      }
    },
    series: Object.entries(typeRollingData.seriesData).map(([name, data], index) => ({
      name,
      data,
      color: name === 'Safe' ? '#10b981' : '#ef4444'
    })),
    credits: {
      enabled: false
    }
  };

  // Add plotOptions for click events
  const natureRollingChartOptionsWithClick = {
    ...natureRollingChartOptions,
    plotOptions: {
      ...natureRollingChartOptions.plotOptions,
      column: {
        ...natureRollingChartOptions.plotOptions?.column,
        point: {
          events: {
            click: function () {
              const month = this.category;
              const type = this.series.name;
              const filtered = allObservations.filter(obs => {
                const obsDate = new Date(obs.reportedDate);
                return format(obsDate, 'MMM') === month && obs.type === type;
              });
              setModalTitle(`Observations for ${type} in ${month}`);
              setModalData(filtered);
              setModalOpen(true);
            }
          }
        }
      }
    }
  };

  // 4. 12-month rolling breakdown by Category of observations
  const categoryRollingChartOptions: Highcharts.Options = {
    chart: {
      type: 'column',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    xAxis: {
      categories: categoryRollingData.months,
      crosshair: true,
      labels: {
        rotation: 0,
        style: {
          fontSize: '13px'
        }
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Observations'
      }
    },
    tooltip: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>{point.y}</b></td></tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true
    },
    plotOptions: {
      column: {
        pointPadding: 0.05,
        groupPadding: 0.1,
        borderWidth: 0
      }
    },
    series: Object.entries(categoryRollingData.seriesData).map(([name, data], index) => ({
      name,
      data,
      color: COLORS[index % COLORS.length]
    })),
    credits: {
      enabled: false
    }
  };

  // Add plotOptions for click events
  const categoryRollingChartOptionsWithClick = {
    ...categoryRollingChartOptions,
    plotOptions: {
      ...categoryRollingChartOptions.plotOptions,
      column: {
        ...categoryRollingChartOptions.plotOptions?.column,
        point: {
          events: {
            click: function () {
              const month = this.category;
              const category = this.series.name;
              const filtered = allObservations.filter(obs => {
                const obsDate = new Date(obs.reportedDate);
                return format(obsDate, 'MMM') === month && obs.category === category;
              });
              setModalTitle(`Observations for ${category} in ${month}`);
              setModalData(filtered);
              setModalOpen(true);
            }
          }
        }
      }
    }
  };

  // 5. Top Observation Reporters
  const reportersChartOptions: Highcharts.Options = {
    chart: {
      type: 'bar',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    xAxis: {
      categories: reporterData.map(item => item.name),
      title: {
        text: null
      },
      labels: {
        style: {
          fontSize: '11px'
        }
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Observations',
        align: 'high'
      },
      labels: {
        overflow: 'justify'
      }
    },
    tooltip: {
      valueSuffix: ' observations'
    },
    plotOptions: {
      bar: {
        dataLabels: {
          enabled: true,
          format: '{y}'
        }
      }
    },
    series: [{
      name: 'Observations',
      data: reporterData.map(item => item.value),
      color: '#3b82f6'
    }],
    credits: {
      enabled: false
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Breakdown of observations by Category */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5" />
                  Breakdown by Category
                </CardTitle>
                <CardDescription>Distribution of observations by category</CardDescription>
              </div>
              <HighchartsContextMenu
                chartTitle="Breakdown by Category"
                chartRef={categoryChartRef}
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] overflow-visible">
              <HighchartsReact
                ref={categoryChartRef}
                highcharts={Highcharts}
                options={categoryChartOptionsWithClick}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Breakdown of observations by Nature */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Breakdown by Nature
            </CardTitle>
            <CardDescription>Distribution of observations by type (Safe/Unsafe)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] overflow-visible">
              <HighchartsReact
                highcharts={Highcharts}
                options={natureChartOptionsWithClick}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>

        {/* 12-month rolling breakdown by Nature */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              12-Month Rolling by Nature
            </CardTitle>
            <CardDescription>Monthly trends by observation type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] overflow-visible">
              <HighchartsReact
                highcharts={Highcharts}
                options={natureRollingChartOptionsWithClick}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>

        {/* 12-month rolling breakdown by Category */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  12-Month Rolling by Category
                </CardTitle>
                <CardDescription>Monthly trends by observation category</CardDescription>
              </div>
              <HighchartsContextMenu
                chartTitle="12-Month Rolling by Category"
                chartRef={rollingChartRef}
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] overflow-visible">
              <HighchartsReact
                ref={rollingChartRef}
                highcharts={Highcharts}
                options={categoryRollingChartOptionsWithClick}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Observation Reporters - Half width, right side empty for future graph */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Top Observation Reporters
                </CardTitle>
                <CardDescription>Users with the most reported observations</CardDescription>
              </div>
              <HighchartsContextMenu
                chartTitle="Top Observation Reporters"
                chartRef={reportersChartRef}
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[450px] overflow-visible">
              <HighchartsReact
                ref={reportersChartRef}
                highcharts={Highcharts}
                options={reportersChartOptions}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>
        <div></div> {/* Empty right column for future graph */}
      </div>

      {/* Dialog */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogContent className="max-w-full w-[98vw] flex flex-col">
          <DialogHeader>
            <DialogTitle>{modalTitle}</DialogTitle>
          </DialogHeader>
          {modalData.length > 5 ? (
            <div className="max-h-[60vh] overflow-auto">
              <ExpandableDataTable data={modalData} columns={observationColumns} paginationSticky />
            </div>
          ) : (
            <ExpandableDataTable data={modalData} columns={observationColumns} paginationSticky />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ObservationAnalyticsCharts; 